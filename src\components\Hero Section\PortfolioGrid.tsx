"use client";
import React from "react";
import { motion } from "framer-motion";
import PortfolioCard from "../Portfolio/PortfolioCard";

// type PortfolioItem = {
//   id: string;
//   type: 'hero-text' | 'image' | 'contact' | 'about' | 'work-showcase' | 'social';
//   gridArea: string;
//   className?: string;
//   content: {
//     title?: string;
//     subtitle?: string;
//     subtitleSuffix?: string;
//     description?: string;
//     image?: string;
//     images?: string[];
//     mainImage?: string;
//     projectList?: string[];
//     link?: string;
//     hasArrow?: boolean;
//     hasFlowerIcon?: boolean;
//     hasCircleIcon?: boolean;
//     backgroundColor?: string;
//     textColor?: string;
//     fontSize?: string;
//     lineHeight?: string;
//     fontFamily?: string;
//     letterSpacing?: string;
//     titleFontSize?: string;
//     subtitleFontSize?: string;
//     titleFontFamily?: string;
//     subtitleFontFamily?: string;
//     items?: string[];
//     socialFrame?: {
//       title: string;
//       subtitle: string;
//       hasArrow: boolean;
//       socialIcons?: string[];
//     };
//     socialLinks?: {
//       platform: string;
//       url: string;
//       label: string;
//     }[];
//   };
// };

// type GridConfig = {
//   columns: number;
//   rows: number;
//   gap: string;
//   areas: string[];
// };

// const portfolioItems: PortfolioItem[] = [
//   {
//     id: 'slogan-intro',
//     type: 'hero-text',
//     gridArea: 'slogan-intro',
//     className: 'bg-[#fadcd9]',
//     content: {
//       title: 'Artist Redefining',
//       subtitle: 'Architecture',
//       subtitleSuffix: 'with AI-Driven Design',
//       textColor: 'text-[#000000]',
//       fontSize: 'text-[56px]',
//       lineHeight: 'leading-[60px]',
//       fontFamily: "font-['Gilroy:Bold',_sans-serif]"
//     }
//   },
//   {
//     id: 'portrait',
//     type: 'image',
//     gridArea: 'portrait',
//     className: '',
//     content: {
//       image: '/img/layput_card-2.png',
//       title: 'Julia Huang',
//     }
//   },
//   {
//     id: 'work',
//     type: 'work-showcase',
//     gridArea: 'work',
//     className: 'bg-[#fadcd9]',
//     content: {
//       title: 'Musea',
//       mainImage: '/img/layput_card-3.png',
//       projectList: ['Elara', 'Verve', 'Zephyr'],
//       textColor: 'text-[#000000]',
//       fontSize: 'text-[25px]',
//       fontFamily: "font-['Gilroy:Medium',_sans-serif]",
//       socialFrame: {
//         title: 'Social Projects',
//         subtitle: 'Follow our journey',
//         hasArrow: true,
//         socialIcons: ['instagram', 'linkedin', 'twitter']
//       }
//     }
//   },
//   {
//     id: 'about',
//     type: 'about',
//     gridArea: 'about',
//     className: 'bg-[#fadcd9]',
//     content: {
//       description: 'Julia Huang is an innovative AI artist, renowned for blending cutting-edge technology with creative expression. Based in LA, she crafts unique digital art experiences accessible globally.',
//       textColor: 'text-[#000000]',
//       fontSize: 'text-[20px]',
//       lineHeight: 'leading-[25px]',
//       fontFamily: "font-['Gilroy:Light',_sans-serif]",
//       hasCircleIcon: true,
//     }
//   },
//   {
//     id: 'contact',
//     type: 'contact',
//     gridArea: 'contact',
//     className: 'bg-[#f8afa6]',
//     content: {
//       title: 'Contact me',
//       subtitle: 'Have some questions?',
//       hasArrow: true,
//       textColor: 'text-[#000000]',
//       titleFontSize: 'text-[55px]',
//       subtitleFontSize: 'text-[15px]',
//       titleFontFamily: "font-['Gilroy:Medium',_sans-serif]",
//       subtitleFontFamily: "font-['Gilroy:Light',_sans-serif]"
//     }
//   },
//   {
//     id: 'social-links',
//     type: 'social',
//     gridArea: 'social-links',
//     className: 'bg-[#fadcd9]',
//     content: {
//       socialLinks: [
//         { platform: 'Instagram', url: 'https://instagram.com', label: 'INSTAGRAM' },
//         { platform: 'Twitter', url: 'https://twitter.com', label: 'TWITTER' },
//         { platform: 'LinkedIn', url: 'https://linkedin.com', label: 'LINKEDIN' },
//       ],
//       textColor: 'text-[#000000]',
//       fontSize: 'text-[15px]',
//       fontFamily: "font-['Gilroy:Light',_sans-serif]"
//     }
//   }
// ];

// const gridConfig = {
//   columns: 12,
//   rows: 6,
//   gap: '16px',
//   areas: [
//     'slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work',
//     'slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work',
//     'slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work',
//     'about about about about contact contact contact contact work work work work',
//     'about about about about contact contact contact contact work work work work',
//     'social-links social-links social-links social-links . . . . . . . .'
//   ]
// };

const PortfolioGrid = () => {
  return (
    <section className="relative w-full px-6 py-16 overflow-hidden">
      {/* Desktop Grid */}
      <div
        className="portfolio-grid w-full max-w-[1400px] mx-auto hidden lg:grid relative"
        style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(12, 1fr)',
          gridTemplateRows: 'repeat(6, minmax(140px, 1fr))',
          gridTemplateAreas: [
            '"slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work"',
            '"slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work"',
            '"slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work"',
            '"about about about about contact contact contact contact work work work work"',
            '"about about about about contact contact contact contact work work work work"',
            '"social-links social-links social-links social-links . . . . . . . ."'
          ].join(' '),
          gap: '16px',
          minHeight: '800px',
        }}
      >
        <div style={{ gridArea: 'slogan-intro' }} className="bg-[#fadcd9] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[56px] leading-[60px] text-[#000]">Artist Redefining</div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[56px] leading-[60px] text-[#000]">Architecture</div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] leading-[40px] text-[#000]">with AI-Driven Design</div>
        </div>
        <div style={{ gridArea: 'portrait' }} className="flex justify-center items-center bg-white rounded-[20px] min-h-[120px]">
          <img src="/img/layput_card-2.png" alt="Julia Huang" className="rounded-full w-32 h-32 object-cover" />
          <div className="ml-6 font-['Gilroy:Bold',_sans-serif] text-[28px] text-[#23283B]">Julia Huang</div>
        </div>
        <div style={{ gridArea: 'work' }} className="bg-[#fadcd9] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px]">
          <div className="font-['Gilroy:Medium',_sans-serif] text-[25px] text-[#000] mb-2">Musea</div>
          <img src="/img/layput_card-3.png" alt="Musea Project" className="rounded-lg w-full max-w-[200px] mb-2" />
          <div className="text-[#000] text-[18px] mb-1">Projects: Elara, Verve, Zephyr</div>
          <div className="mt-2">
            <div className="font-['Gilroy:Medium',_sans-serif] text-[16px] text-[#000]">Social Projects</div>
            <div className="text-[14px] text-[#000]">Follow our journey</div>
            <div className="flex gap-2 mt-1">
              <span>Instagram</span>
              <span>LinkedIn</span>
              <span>Twitter</span>
            </div>
          </div>
        </div>
        <div style={{ gridArea: 'about' }} className="bg-[#fadcd9] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px]">
          <div className="font-['Gilroy:Light',_sans-serif] text-[20px] text-[#000]">
            Julia Huang is an innovative AI artist, renowned for blending cutting-edge technology with creative expression. Based in LA, she crafts unique digital art experiences accessible globally.
          </div>
        </div>
        <div style={{ gridArea: 'contact' }} className="bg-[#f8afa6] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px]">
          <div className="font-['Gilroy:Medium',_sans-serif] text-[55px] text-[#000] mb-2">Contact me</div>
          <div className="font-['Gilroy:Light',_sans-serif] text-[15px] text-[#000] mb-2">Have some questions?</div>
          <div className="flex items-center gap-2">
            <span className="font-['Gilroy:Medium',_sans-serif] text-[16px] text-[#000]">Send an Email</span>
            <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M7 17L17 7M17 7H7M17 7V17" /></svg>
          </div>
        </div>
        <div style={{ gridArea: 'social-links' }} className="bg-[#fadcd9] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px]">
          <div className="font-['Gilroy:Light',_sans-serif] text-[15px] text-[#000] uppercase flex gap-4">
            <span>INSTAGRAM</span>
            <span>TWITTER</span>
            <span>LINKEDIN</span>
          </div>
        </div>
      </div>
      {/* Tablet and Mobile Grids can be similarly hardcoded if needed */}
    </section>
  );
}

export default PortfolioGrid;
