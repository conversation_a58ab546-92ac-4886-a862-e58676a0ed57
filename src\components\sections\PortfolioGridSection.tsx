"use client";
import React from "react";
import { motion } from "framer-motion";

const PortfolioGridSection = () => {
  return (
    <section className="relative w-full px-6 py-16 overflow-hidden">
      {/* Desktop Grid */}
      <div
        className="portfolio-grid w-full max-w-[1400px] mx-auto hidden lg:grid relative"
        style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(12, 1fr)',
          gridTemplateRows: 'repeat(6, minmax(140px, 1fr))',
          gridTemplateAreas: [
            '"slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work"',
            '"slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work"',
            '"slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work"',
            '"about about about about contact contact contact contact work work work work"',
            '"about about about about contact contact contact contact work work work work"',
            '"social-links social-links social-links social-links . . . . . . . ."'
          ].join(' '),
          gap: '16px',
          minHeight: '800px',
        }}
      >
        {/* Artist Redefining Architecture Card */}
        <div style={{ gridArea: 'slogan-intro' }} className="bg-[#fadcd9] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[56px] leading-[60px] text-[#000]">Artist Redefining</div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[56px] leading-[60px] text-[#000]">
            <span className="italic">Architecture</span> <span className="font-normal">with</span>
          </div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] leading-[40px] text-[#000]">AI-Driven Design</div>
          {/* Decorative flower pattern */}
          <div className="absolute top-4 right-4 w-16 h-16 opacity-20">
            <svg viewBox="0 0 64 64" className="w-full h-full">
              <path d="M32 8 L40 24 L56 32 L40 40 L32 56 L24 40 L8 32 L24 24 Z" fill="currentColor" opacity="0.3"/>
            </svg>
          </div>
        </div>

        {/* Portrait Card */}
        <div style={{ gridArea: 'portrait' }} className="flex flex-col justify-center items-center bg-white rounded-[20px] min-h-[120px] p-6">
          <img src="/img/layput_card-2.png" alt="Portrait" className="rounded-lg w-full max-w-[200px] object-cover mb-4" />
        </div>

        {/* Musea Project Card */}
        <div style={{ gridArea: 'work' }} className="bg-[#fadcd9] flex flex-col justify-start items-start p-6 rounded-[20px] min-h-[120px] relative">
          <div className="flex justify-between items-start w-full mb-4">
            <h3 className="font-['Gilroy:Medium',_sans-serif] text-[28px] text-[#000]">Musea</h3>
            <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" className="text-[#000]">
              <path d="M7 17L17 7M17 7H7M17 7V17" />
            </svg>
          </div>
          
          <div className="grid grid-cols-2 gap-3 w-full mb-4">
            <img src="/img/layput_card-3.png" alt="Musea Project 1" className="rounded-lg w-full h-20 object-cover" />
            <img src="/img/layput_card-2.png" alt="Musea Project 2" className="rounded-lg w-full h-20 object-cover" />
          </div>

          <div className="space-y-2">
            <div className="font-['Gilroy:Medium',_sans-serif] text-[18px] text-[#000]">Elara</div>
            <div className="font-['Gilroy:Medium',_sans-serif] text-[18px] text-[#000]">Verve</div>
            <div className="font-['Gilroy:Medium',_sans-serif] text-[18px] text-[#000]">Zephyr</div>
          </div>
        </div>

        {/* PER PIXEL About Card */}
        <div style={{ gridArea: 'about' }} className="bg-[#fadcd9] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[48px] leading-[52px] text-[#000] mb-4">PER PIXEL</div>
          <div className="font-['Gilroy:Light',_sans-serif] text-[16px] text-[#000] leading-relaxed">
            Julia Huang is an innovative AI artist, renowned for blending cutting-edge technology with creative expression. Based in LA, she crafts unique digital art experiences accessible globally.
          </div>
        </div>

        {/* Contact Card */}
        <div style={{ gridArea: 'contact' }} className="bg-[#f8afa6] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px] relative">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[48px] leading-[52px] text-[#000] mb-2">Contact me</div>
          <div className="font-['Gilroy:Light',_sans-serif] text-[16px] text-[#000] mb-4">Have some questions?</div>
          <div className="flex items-center gap-2 cursor-pointer hover:opacity-80 transition-opacity">
            <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" className="text-[#000]">
              <path d="M7 17L17 7M17 7H7M17 7V17" />
            </svg>
          </div>
        </div>

        {/* Social Links Card */}
        <div style={{ gridArea: 'social-links' }} className="bg-[#fadcd9] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px]">
          <div className="font-['Gilroy:Medium',_sans-serif] text-[14px] text-[#000] uppercase space-y-2">
            <div className="cursor-pointer hover:opacity-70 transition-opacity">INSTAGRAM</div>
            <div className="cursor-pointer hover:opacity-70 transition-opacity">TWITTER</div>
            <div className="cursor-pointer hover:opacity-70 transition-opacity">LINKEDIN</div>
          </div>
        </div>
      </div>

      {/* Tablet Grid - Simplified responsive version */}
      <div className="hidden md:grid lg:hidden grid-cols-6 gap-4 w-full max-w-[900px] mx-auto">
        <div className="col-span-3 bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] leading-[36px] text-[#000]">Artist Redefining</div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] leading-[36px] text-[#000]">
            <span className="italic">Architecture</span> <span className="font-normal">with</span>
          </div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[24px] leading-[28px] text-[#000]">AI-Driven Design</div>
        </div>
        <div className="col-span-3 bg-white p-6 rounded-[20px] flex justify-center">
          <img src="/img/layput_card-2.png" alt="Portrait" className="rounded-lg max-w-[150px] object-cover" />
        </div>
        <div className="col-span-2 bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] text-[#000]">PER PIXEL</div>
        </div>
        <div className="col-span-2 bg-[#f8afa6] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] text-[#000]">Contact me</div>
        </div>
        <div className="col-span-2 bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Medium',_sans-serif] text-[20px] text-[#000]">Musea</div>
        </div>
      </div>

      {/* Mobile Grid - Stack vertically */}
      <div className="grid md:hidden grid-cols-1 gap-4 w-full max-w-[400px] mx-auto">
        <div className="bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[24px] leading-[28px] text-[#000]">Artist Redefining</div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[24px] leading-[28px] text-[#000]">
            <span className="italic">Architecture</span> <span className="font-normal">with</span>
          </div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[20px] leading-[24px] text-[#000]">AI-Driven Design</div>
        </div>
        <div className="bg-white p-6 rounded-[20px] flex justify-center">
          <img src="/img/layput_card-2.png" alt="Portrait" className="rounded-lg max-w-[120px] object-cover" />
        </div>
        <div className="bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[24px] text-[#000]">PER PIXEL</div>
        </div>
        <div className="bg-[#f8afa6] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[24px] text-[#000]">Contact me</div>
        </div>
        <div className="bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Medium',_sans-serif] text-[18px] text-[#000]">Musea</div>
        </div>
      </div>
    </section>
  );
};

export default PortfolioGridSection;
