"use client";
import React from 'react';
import { motion } from 'framer-motion';
import SocialMediaLinks from './SocialMediaLinks';
import ArrowElement from './ArrowElement';
import PortfolioText from './PortfolioText';
import QuickLinks from './QuickLinks';

/**
 * PortfolioSection Component
 *
 * Portfolio showcase section matching the reference design layout.
 *
 * Layout Structure:
 * - Left side: Navigation arrows and hashtag text content
 * - Right side: Main orange rectangle with portfolio content
 * - Bottom: Quick links section with BLOG buttons
 *
 * Design Features:
 * - Asymmetric layout with 60-70% content on right
 * - Orange rounded containers (#FEB273)
 * - Black navigation arrows and hashtag text
 * - Responsive design for different screen sizes
 */
const PortfolioSection = () => {
  return (
    <section id="portfolio" className="relative w-full min-h-screen bg-white overflow-hidden py-16">
      {/* Main container with asymmetric layout */}
      <div className="relative w-full max-w-7xl mx-auto px-8">

        {/* Top section with left navigation and right main content */}
        <div className="relative flex items-start justify-between mb-16">

          {/* Left side - Navigation arrows and hashtag text */}
          <div className="w-[35%] flex flex-col items-start justify-start space-y-12 pt-8">

            {/* Large navigation arrow */}
            <div className="relative ml-8">
              <ArrowElement />
            </div>

            {/* Hashtag text content - positioned below arrow */}
            <div className="space-y-6 ml-4">
              <motion.div
                className="flex flex-wrap gap-x-12 gap-y-3"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <span className="text-black font-['Gilroy:Medium'] text-xl tracking-wide">#BRANDING</span>
                <span className="text-black font-['Gilroy:Medium'] text-xl tracking-wide">#LOGO</span>
              </motion.div>
              <motion.div
                className="flex flex-wrap gap-x-12 gap-y-3"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                viewport={{ once: true }}
              >
                <span className="text-black font-['Gilroy:Medium'] text-xl tracking-wide">#SOCIAL_MEDIA</span>
                <span className="text-black font-['Gilroy:Medium'] text-xl tracking-wide">#POSTER</span>
              </motion.div>
              <motion.div
                className="flex flex-wrap gap-x-12 gap-y-3"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.7 }}
                viewport={{ once: true }}
              >
                <span className="text-black font-['Gilroy:Medium'] text-xl tracking-wide">#Illustration</span>
                <span className="text-black font-['Gilroy:Medium'] text-xl tracking-wide">#Packaging</span>
              </motion.div>
            </div>

          </div>

          {/* Right side - Main orange content rectangle */}
          <div className="w-[60%]">
            <motion.div
              className="relative bg-[#FEB273] rounded-[20px] p-8 min-h-[500px] overflow-hidden"
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >

              {/* Social Media Links at top */}
              <div className="absolute top-6 left-1/2 transform -translate-x-1/2 z-10">
                <SocialMediaLinks />
              </div>

              {/* Main portfolio text content */}
              <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
                <PortfolioText />
              </div>

            </motion.div>
          </div>

        </div>


        {/* Bottom section - Quick Links */}
        <div className="w-full">
          <QuickLinks />
        </div>

      </div>
    </section>
  );
};

export default PortfolioSection;
