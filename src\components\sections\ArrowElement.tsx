"use client";
import React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';

/**
 * ArrowElement Component
 *
 * Large arrow graphic positioned in the left white space area.
 * Points toward the orange portfolio container to guide user attention.
 *
 * Design Features:
 * - Uses the provided Arrow 1.png image from public/img/
 * - Positioned outside the orange container in the left white space
 * - Smooth entrance animation with rotation effect
 * - Hover interactions for enhanced user experience
 * - Subtle pulse animation for visual interest
 */
const ArrowElement = () => {
  return (
    <div>
      {/*
        Main Arrow Container
        Positioned in the center of the left white space area
        Entrance animation slides in from left with rotation
      */}
      <motion.div
        className="relative flex items-center justify-center"
        initial={{ opacity: 0, x: -50, rotate: -10 }}
        whileInView={{ opacity: 1, x: 0, rotate: 0 }}
        transition={{ duration: 1, delay: 0.5 }}
        viewport={{ once: true }}
      >
      {/*
        Arrow Image Container
        Uses the provided Arrow 1.png from the public/img directory
        Hover effect adds subtle scale and rotation for interactivity
      */}
      <motion.div
        className="relative"
        whileHover={{ scale: 1.1, rotate: 5 }}
        transition={{ duration: 0.3 }}
      >
        <Image
          src="/img/Arrow 1.png"
          alt="Arrow pointing to portfolio"
          width={200}
          height={200}
          className="object-contain filter drop-shadow-lg"
          priority
        />
      </motion.div>

      {/*
        Animated Pulse Overlay
        Adds subtle visual interest with a pulsing border effect
        Helps draw attention to the arrow element
        Infinite animation loop for continuous engagement
      */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        initial={{ scale: 0.8, opacity: 0 }}
        whileInView={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.8 }}
        viewport={{ once: true }}
      >
        {/*
          Pulse Effect Circle
          Creates a subtle breathing effect around the arrow
          Low opacity to avoid overwhelming the main arrow image
        */}
        <motion.div
          className="absolute inset-0 rounded-full border-2 border-black opacity-20"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.1, 0.2],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </motion.div>
      </motion.div>
    </div>
  );
};

export default ArrowElement;
